<?php
/*
Template Name: <PERSON><PERSON><PERSON> specjalistyczny branżowy - podstrona
*/

get_header();

global $post;

use DD\App\Meta\Sections\HeroSection;
use DD\App\Meta\Sections\SectionWithoutImage;
use DD\App\Meta\Sections\StepsSection;
use DD\App\Meta\Sections\ExamInfoSection;
use DD\App\Meta\Sections\FaqFromStepsSection;
use DD\App\Meta\Sections\ExtraExamInfoSection;
use DD\App\Meta\Config;

$hero = new HeroSection($post);
$sectionWithoutImage = new SectionWithoutImage($post);
$steps = new StepsSection($post);
$examInfo = new ExamInfoSection($post);
$faq = new FaqFromStepsSection($post);
$examInfoSecond = new ExtraExamInfoSection($post);

$hero->render();

?>

<main id="main-content" class="template dd-container">
    <?php

    $sectionWithoutImage->render();
    $steps ->render();
    $examInfo->render();
    $faq->render();
    $examInfoSecond->render();
    
    ?>

    <section class="section-newsletter">
        <div class="left-right">
            <div class="left">
                <div class="newsletter full-width">
                    <div class="left-side">
                        <h2><?php _e('Zacznijmy współpracę, która naprawdę <span class="color-secondary">przynosi efekty</span>', DD_TEXTDOMAIN) ?>
                        </h2>
                        <p><?php _e('Gotów na językowy rozwój Twojej kariery?', DD_TEXTDOMAIN) ?><br></br>
                            <?php _e('<strong>Skontaktuj się z nami i wypełnij formularz kontaktowy!</strong>', DD_TEXTDOMAIN) ?>
                        </p>
                    </div>
                    <div class="right-side">
                        <img src="<?php echo get_template_directory_uri() . '/assets/img/newsletter_img.webp' ?>"
                            alt="<?= esc_attr__('Obrazek newslettera', DD_TEXTDOMAIN) ?>" />
                    </div>
                </div>
            </div>
            <div class="wrapper">
                <div class="right" id="form">
                    <h2 class="form-title">Formularz kontaktowy - język branżowy</h2>
                    <?php
                    $contact_form_shortcode = get_post_meta($post->ID, Config::FIELD_CONTACT_FORM_SHORTCODE, true);

                    if (!empty($contact_form_shortcode)) {
                        echo do_shortcode($contact_form_shortcode);
                    } else {
                        echo do_shortcode('[contact-form-7 id="9ebdf46" title="Angielski branżowy - krokowy"]');
                    }
                    ?>
                </div>
            </div>
        </div>
    </section>
</main>

<script>
jQuery(document).ready(function($) {
    $(".faq-item").on("click", function() {
        const $clicked = $(this);

        if ($clicked.hasClass("open")) {
            $clicked.removeClass("open").find(".faq-answer").slideUp(300, "swing");
        } else {
            $(".faq-item").removeClass("open").find(".faq-answer").slideUp(300, "swing");
            $clicked.addClass("open").find(".faq-answer").slideDown(300, "swing");
        }
    });
});

jQuery(document).ready(function($) {
    $(".dd-exam-info-section .item").on("click", function() {
        if ($(window).width() >= 991) {
            return;
        }

        const $item = $(this);
        const $content = $item.find(".content");

        $item.toggleClass("open");

        if ($content.is(":visible")) {
            $content.slideUp(300, "swing");
        } else {
            $content
                .css({
                    display: "block"
                })
                .hide()
                .slideDown(300, "swing");
        }
    });
});
</script>

<?php get_footer(); ?>