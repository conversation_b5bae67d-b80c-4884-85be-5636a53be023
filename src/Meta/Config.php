<?php

namespace DD\App\Meta;

final class Config {
    // Template
    public const TEMPLATE_FILENAME = 'templates/page-template-specialized-single.php';

    // Metabox
    public const META_BOX_ID = 'dd_specialized_hero';
    public const META_BOX_TITLE = '<PERSON><PERSON><PERSON> sekcja - Hero';

    // Nonce
    public const NONCE_FIELD = 'dd_specialized_nonce';
    public const NONCE_ACTION = 'dd_specialized_save_hero';

    // HERO
    public const FIELD_TITLE = '_dd_hero_title';
    public const FIELD_SUBTITLE = '_dd_hero_subtitle';
    public const FIELD_IMAGE_ID = '_dd_hero_image_id';
    public const FIELD_IMAGE_ID_MOBILE = '_dd_hero_image_id_mobile';

    // Section Without Image
    public const FIELD_SECTION_TITLE_LEFT = '_dd_section_title_left';
    public const FIELD_SECTION_SUBTITLE_LEFT = '_dd_section_subtitle_left';
    public const FIELD_SECTION_RIGHT_CONTENT = '_dd_section_right_content';

    // Steps
    public const FIELD_STEP_TITLE       = 'dd_step_title_%d';
    public const FIELD_STEP_DESCRIPTION = 'dd_step_description_%d';
    public const FIELD_STEP_ICON_ID     = 'dd_step_icon_id_%d';

    // Contact Form
    public const FIELD_CONTACT_FORM_SHORTCODE = '_dd_contact_form_shortcode';

    // Exam Info
    public const CONFIG_STEPS = 7;
    public const EXAM_INFO_POINTS = 6;
    public const EXTRA_INFO_POINTS = 5;
    public const FIELD_EXAM_INFO_TITLE   = 'dd_exam_info_title_%d';
    public const FIELD_EXAM_INFO_CONTENT = 'dd_exam_info_content_%d';
    public const FIELD_EXTRA_INFO_TITLE = 'dd_extra_info_title_%d';
    public const FIELD_EXTRA_INFO_CONTENT = 'dd_extra_info_content_%d';
}